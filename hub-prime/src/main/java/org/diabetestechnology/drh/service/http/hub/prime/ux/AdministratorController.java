package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.diabetestechnology.drh.service.http.hub.prime.route.RouteMapping;
import org.diabetestechnology.drh.service.http.pg.security.RequiresUserPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

@Controller
@Tag(name = "Administrator", description = "Administrator API")
public class AdministratorController {
    private final Presentation presentation;
    private static final Logger LOG = LoggerFactory.getLogger(AdministratorController.class);

    public AdministratorController(Presentation presentation) {
        this.presentation = presentation;
    }

    @RouteMapping(label = "Administration", siblingOrder = 140)
    @GetMapping("/administration")
    @RequiresUserPermission(permission = "ADMINISTRATION")
    public String viewAuditLogs(Model model) {
        return "redirect:/administration/info";
    }

    @GetMapping("/administration/info")
    @RouteMapping(label = "User Settings", title = "User Settings", siblingOrder = 0)
    public String svmFinal(Model model, final HttpServletRequest request) {
        LOG.info("Read User Settings");
        return presentation.populateModel("page/investigator/usersettings", model, request);
    }

    // @GetMapping("/administration/database")
    // @RouteMapping(label = "Database File", title = "Database File", siblingOrder
    // = 10)
    // public String userSettings(Model model, final HttpServletRequest request) {
    // LOG.info("Database File Extraction Progress");
    // return presentation.populateModel("page/database/extractionStatus", model,
    // request);

    // }

}
