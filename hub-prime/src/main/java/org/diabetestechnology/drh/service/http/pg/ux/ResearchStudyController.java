package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.Response;
import org.diabetestechnology.drh.service.http.pg.request.PublicationUpdateRequest;
import org.diabetestechnology.drh.service.http.pg.request.ResearchStudySettingsRequest;
import org.diabetestechnology.drh.service.http.pg.request.StudyRequest;
import org.diabetestechnology.drh.service.http.pg.security.Permission;
import org.diabetestechnology.drh.service.http.pg.security.RequiresPermission;
import org.diabetestechnology.drh.service.http.pg.service.ResearchStudyService;
import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.JSONB;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Controller
@Tag(name = "DRH Research Study APIs")
public class ResearchStudyController {

    private final ResearchStudyService researchStudyService;
    private final UserNameService userNameService;

    public ResearchStudyController(ResearchStudyService researchStudyService, UserNameService userNameService) {
        this.researchStudyService = researchStudyService;
        this.userNameService = userNameService;
    }

    private static final Logger LOG = LoggerFactory.getLogger(ResearchStudyController.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @PostMapping("/research-studies")
    @Operation(summary = "Save a study")
    @ResponseBody
    public Response saveResearchStudy(@RequestBody StudyRequest request) {
        LOG.info("save research study: {}", request);
        try {
            if (userNameService.getCurrentuserPartyId() == null) {
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("Failed to save a research study due to Unauthorized access.")
                        .errors("Failed to save a research study due to Unauthorized access.")
                        .build();
            }
            String studyResponse = researchStudyService.saveResearchStudy(request);
            JsonNode responseJson = OBJECT_MAPPER.readTree(studyResponse);

            if (responseJson.has("status") && "failure".equals(responseJson.get("status").asText())) {
                String errorMessage = responseJson.has("message") ? responseJson.get("message").asText()
                        : "Unknown error occurred.";
                JsonNode errorDetails = responseJson.has("error_details") ? responseJson.get("error_details") : null;
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message(errorMessage)
                        .errors(errorDetails != null ? errorDetails.toString() : null)
                        .build();
            }
            return Response.builder()
                    .data(Map.of("studyId",
                            studyResponse))
                    .status("success")
                    .message("Successfully saved a research study.")
                    .errors(null)
                    .build();
        } catch (IllegalArgumentException e) {
            LOG.error("Validation error: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to save a research study due to validation error.")
                    .errors("Failed to save a research study due to validation error." + e
                            .getMessage())
                    .build();
        } catch (Exception e) {
            LOG.error("Error while calling the function: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to save a research study.")
                    .errors("Failed to save a research study : " + e
                            .getMessage())
                    .build();
        }
    }

    @PutMapping("/research-studies/study-data/{studyId}")
    @Operation(summary = "Edit inline study data")
    @ResponseBody
    public Response editResearchStudy(@PathVariable String studyId, @RequestBody JsonNode studyData) {
        if (!userNameService.getCurrentuserPartyId().equalsIgnoreCase(researchStudyService.getStudyOwner(studyId))) {
            LOG.warn("Access denied: User {} is not the owner of study {}", userNameService.getCurrentuserPartyId(),
                    studyId);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Access denied: Only the study owner is permitted to edit this research study.")
                    .errors(null)
                    .build();
        }
        if (researchStudyService.getResearchStudyArchiveStatus(studyId)) {
            LOG.warn("Access denied: Study {} is archived.", studyId);

            Response response = Response.builder()
                    .status("failure")
                    .message("The study is archived; edits and updates are not allowed.")
                    .build();

            return response;
        }
        LOG.info("edit research study: {}", studyData);
        try {
            JSONB studyResponse = researchStudyService.editResearchStudy(studyId, JSONB.valueOf(studyData.toString()));
            JsonNode responseJson = OBJECT_MAPPER.readTree(studyResponse.toString());

            if (responseJson.has("status") && "failure".equals(responseJson.get("status").asText())) {
                String errorMessage = responseJson.has("message") ? responseJson.get("message").asText()
                        : "Unknown error occurred.";
                JsonNode errorDetails = responseJson.has("error_details") ? responseJson.get("error_details") : null;
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message(errorMessage)
                        .errors(errorDetails != null ? errorDetails.toString() : null)
                        .build();
            }
            return Response.builder()
                    .data(Map.of("response",
                            JsonUtils.jsonStringToMapOrList(studyResponse.data())))
                    .status("success")
                    .message("Successfully edited inline data of the research study.")
                    .errors(null)
                    .build();
        } catch (IllegalArgumentException e) {
            LOG.error("Validation error: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to edit inline data of a research study due to invalid input.")
                    .errors("Failed to edit inline data of a research study due to invalid input." + e
                            .getMessage())
                    .build();
        } catch (Exception e) {
            LOG.error("Error while calling the function: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to edit inline data of a research study.")
                    .errors("Failed to edit inline data of a research study:" + e
                            .getMessage())
                    .build();
        }
    }

    @PostMapping("/research-study-settings")
    @Operation(summary = "Save study settings")
    @ResponseBody
    public Response saveResearchStudySettings(@RequestBody ResearchStudySettingsRequest request) {

        try {
            if (!userNameService.getCurrentuserPartyId()
                    .equalsIgnoreCase(researchStudyService.getStudyOwner(request.studyId()))) {
                LOG.warn("Access denied: User {} is not the owner of study {}", userNameService.getCurrentuserPartyId(),
                        request.studyId());
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("Access denied: Only the study owner is permitted to edit this research study.")
                        .errors(null)
                        .build();
            }
            if (researchStudyService.getResearchStudyArchiveStatus(request.studyId())) {
                LOG.warn("Access denied: Study {} is archived.", request.studyId());

                Response response = Response.builder()
                        .status("failure")
                        .message("The study is archived; edits and updates are not allowed.")
                        .build();

                return response;
            }
            LOG.info("save research study settings: {}", request);

            JSONB studyResponse = researchStudyService.saveResearchStudySettings(request);
            String responseString = studyResponse.toString();
            JsonNode responseJson = OBJECT_MAPPER.readTree(responseString);

            if (responseJson.has("status") && "failure".equals(responseJson.get("status").asText())) {
                String errorMessage = responseJson.has("message") ? responseJson.get("message").asText()
                        : "Unknown error occurred.";
                JsonNode errorDetails = responseJson.has("error_details") ? responseJson.get("error_details") : null;
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message(errorMessage)
                        .errors(errorDetails != null ? errorDetails.toString() : null)
                        .build();
            }
            return Response.builder()
                    .data(Map.of("studyId",
                            JsonUtils.jsonStringToMapOrList(studyResponse.data())))
                    .status("success")
                    .message("Successfully saved research study settings.")
                    .errors(null)
                    .build();
        } catch (IllegalArgumentException e) {
            LOG.error("Validation error: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed  to save research study settings due to invalid input.")
                    .errors("Failed  to save research study settings due to invalid input." + e
                            .getMessage())
                    .build();
        } catch (Exception e) {
            LOG.error("Error while calling the function: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed  to save research study settings.")
                    .errors("Failed  to save research study settings: " + e
                            .getMessage())
                    .build();
        }

    }

    @GetMapping("/research-study")
    @ResponseBody
    @Operation(summary = "find a study")
    public Response getResearchStudies(@RequestParam String studyId) {
        LOG.info("get research study: {}", studyId);
        try {
            JSONB result = researchStudyService.getResearchStudies(studyId).join();

            return Response.builder()
                    .data(Map.of("studyDetails", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Successfully read study details")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to read study details")
                    .errors("Error in reading study details: " +
                            e.getMessage())
                    .build();
        }
    }

    @GetMapping("/research-study/my")
    @ResponseBody
    // @RequiresPermission(resource = "STUDIES", permissions = {
    // @Permission(permissionName = "My Studies", action = "VIEW/EDIT") })
    @Operation(summary = "find my research studies")
    public Response getMyResearchStudies(@RequestParam String userId) {
        LOG.info("get my research study: {}", userId);
        try {
            JSONB result = researchStudyService.getMyResearchStudies(userId);

            JsonNode responseJson = OBJECT_MAPPER.readTree(result.toString());
            if (responseJson.has("status") && "failure".equals(responseJson.get("status").asText())) {
                String errorMessage = responseJson.has("message") ? responseJson.get("message").asText()
                        : "Unknown error occurred.";
                JsonNode errorDetails = responseJson.has("error_details") ? responseJson.get("error_details") : null;
                LOG.error("Error fetching my research study : " + errorMessage);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message(errorMessage)
                        .errors(errorDetails != null ? errorDetails.toString() : null)
                        .build();
            }
            return Response.builder()
                    .data(Map.of("studyDetails", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Successfully read my study details")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to read my study details")
                    .errors("Error in reading my study details: " +
                            e.getMessage())
                    .build();
        }
    }

    @GetMapping("/research-study/all")
    @ResponseBody
    @Operation(summary = "find all research studies")
    public Response getAllResearchStudies(@RequestParam String userId) {
        LOG.info("get all research study: {}", userId);
        try {
            JSONB result = researchStudyService.getAllResearchStudies(userId);
            JsonNode responseJson = OBJECT_MAPPER.readTree(result.toString());
            if (responseJson.has("status") && "failure".equals(responseJson.get("status").asText())) {
                String errorMessage = responseJson.has("message") ? responseJson.get("message").asText()
                        : "Unknown error occurred.";
                JsonNode errorDetails = responseJson.has("error_details") ? responseJson.get("error_details") : null;
                LOG.error("Error fetching all research study : " + errorMessage);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message(errorMessage)
                        .errors(errorDetails != null ? errorDetails.toString() : null)
                        .build();
            }
            return Response.builder()
                    .data(Map.of("studyDetails", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Successfully read all study details")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to read all study details")
                    .errors("Error in reading all study details: " +
                            e.getMessage())
                    .build();
        }
    }

    @PutMapping("/research-studies/archive-status/{studyId}")
    @Operation(summary = "Change Archive status")
    @ResponseBody
    public Response editResearchStudyArchiveStatus(@PathVariable String studyId,
            @RequestParam Boolean isArchived) {
        LOG.info("edit research study archive status: {}", studyId);
        try {
            if (!userNameService.getCurrentuserPartyId()
                    .equalsIgnoreCase(researchStudyService.getStudyOwner(studyId))) {
                LOG.warn("Access denied: User {} is not the owner of study {}", userNameService.getCurrentuserPartyId(),
                        studyId);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message(
                                "Access denied: Only the study owner is permitted to change archive status research study.")

                        .errors(null)
                        .build();
            }
            JSONB studyResponse = researchStudyService.editResearchStudyArchiveStatus(studyId,
                    isArchived);
            JsonNode responseJson = OBJECT_MAPPER.readTree(studyResponse.toString());
            if (responseJson.has("status") && "failure".equals(responseJson.get("status").asText())) {
                String errorMessage = responseJson.has("message") ? responseJson.get("message").asText()
                        : "Unknown error occurred.";
                JsonNode errorDetails = responseJson.has("error_details") ? responseJson.get("error_details") : null;
                LOG.error("Error updating archive status : " + errorMessage);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message(errorMessage)
                        .errors(errorDetails != null ? errorDetails.toString() : null)
                        .build();
            }
            return Response.builder()
                    .data(Map.of("studyResponse", JsonUtils.jsonStringToMapOrList(studyResponse.data())))
                    .status("success")
                    .message("Successfully changed archive status")
                    .errors(null)
                    .build();
        } catch (IllegalArgumentException e) {
            LOG.error("Validation error: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to change archive status due to invalid input")
                    .errors("Failed to change archive status due to invalid input: " + e
                            .getMessage())
                    .build();
        } catch (Exception e) {
            LOG.error("Error while calling the function: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to change archive status")
                    .errors("Failed to change archive status due to : " + e
                            .getMessage())
                    .build();
        }
    }

    @GetMapping("/research-study/study-team")
    @ResponseBody
    @Operation(summary = "find all members of Study Team")
    public Response getAllResearchStudyTeam(@RequestParam String studyId) {
        LOG.info("get all research study team: {}", studyId);
        try {
            JSONB result = researchStudyService.getAllResearchStudyTeam(studyId);
            return Response.builder()
                    .data(Map.of("studyTeam", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Successfully read all study team")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to read all study team")
                    .errors("Error in reading all study team: " +
                            e.getMessage())
                    .build();
        }
    }

    @SuppressWarnings("unchecked")
    @PutMapping("/research-study/publication-author")
    @Operation(summary = "Edit inline data of publication and author corresponds to a research study.")
    @ResponseBody
    public Response updateAuthorPublicationInline(
            @RequestBody PublicationUpdateRequest request, @RequestParam String citationID) {

        try {
            String studyId = request.collaboration_team().studyId();
            if (!userNameService.getCurrentuserPartyId()
                    .equalsIgnoreCase(researchStudyService.getStudyOwner(studyId))) {
                LOG.warn("Access denied: User {} is not the owner of study {}", userNameService.getCurrentuserPartyId(),
                        studyId);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("Access denied: Only the study owner is permitted to edit this research study.")
                        .errors(null)
                        .build();
            }
            if (researchStudyService.getResearchStudyArchiveStatus(studyId)) {
                LOG.warn("Access denied: Study {} is archived.", studyId);

                return Response.builder()
                        .status("failure")
                        .message("The study is archived; edits and updates are not allowed.")
                        .build();
            }
            Map<String, Object> responseMap = new HashMap<>();

            JSONB publicationResponse = researchStudyService.updatePublicationInline(studyId, request, citationID);
            Map<String, Object> publicationMap = OBJECT_MAPPER.readValue(publicationResponse.data(), Map.class);
            responseMap.put("publicationResponse", publicationMap);

            Object authorResponse = researchStudyService.saveAuthors(
                    studyId,
                    request.collaboration_team().coAuthorsName(),
                    citationID);

            Map<String, Object> authorMap;
            if (authorResponse instanceof JSONB jsonb) {
                authorMap = OBJECT_MAPPER.readValue(jsonb.data(), Map.class);
            } else {
                authorMap = OBJECT_MAPPER.readValue(authorResponse.toString(), Map.class);
            }
            responseMap.put("authorResponse", authorMap);

            boolean isPublicationSuccess = "success".equalsIgnoreCase((String) publicationMap.get("status"));
            boolean isAuthorSuccess = "success".equalsIgnoreCase((String) authorMap.get("status"));

            if (!isPublicationSuccess && !isAuthorSuccess) {
                return Response.builder()
                        .status("error")
                        .message("Failed to update publication and author.")
                        .data(responseMap)
                        .errors("Failed to update publication and author.")
                        .build();
            }

            String message;
            if (isPublicationSuccess && isAuthorSuccess) {
                message = "Publication and author information updated successfully.";
            } else if (isPublicationSuccess) {
                message = "Publication updated successfully; failed to update author information.";
            } else {
                message = "Author information updated successfully; failed to update publication.";
            }

            LOG.info("Partial/full success updating publication and/or authors for studyId: {}. Responses: {}", studyId,
                    responseMap);

            return Response.builder()
                    .status("success")
                    .message(message)
                    .data(responseMap)
                    .errors(null)
                    .build();
        } catch (IllegalArgumentException e) {
            LOG.error("Validation error: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to update inline publication data of a research study due to invalid input")
                    .errors("Failed to update inline publication data of a research study due to invalid input: "
                            + e.getMessage())
                    .build();
        } catch (Exception e) {
            LOG.error("Error while calling the function: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to update inline publication data of a research study.")
                    .errors("Failed to update inline publication data of a research study: "
                            + e.getMessage())
                    .build();
        }

    }

    @GetMapping("/research-study/principal-investigator")
    @ResponseBody
    @Operation(summary = "find principal investigator of Study Team")
    public Response getAllResearchStudyPrincipalInvestigator(@RequestParam String studyId) {
        LOG.info("get all Primary Investigators of Study Team: {}", studyId);
        try {
            JSONB result = researchStudyService.getAllResearchStudyPrincipalInvestigator(studyId);
            return Response.builder()
                    .data(Map.of("studyTeam", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Successfully Read Principal Investigator of Study Team")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to read all Principal Investigators of Study Team")
                    .errors("Error in reading Principal Investigators of Study Team: " +
                            e.getMessage())
                    .build();
        }
    }

    @PutMapping("/research-study/{studyId}/visibility")
    @Operation(summary = "Update ResearchStudy Visibility")
    @ResponseBody
    public Response updateResearchStudyVisibility(@PathVariable String studyId,
            @RequestParam("studyVisibilityId") Integer studyVisibilityId) {
        if (!userNameService.getCurrentuserPartyId().equalsIgnoreCase(researchStudyService.getStudyOwner(studyId))) {
            LOG.warn("Access denied: User {} is not the owner of study {}", userNameService.getCurrentuserPartyId(),
                    studyId);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Access denied: Only the study owner is permitted to edit this research study.")
                    .errors(null)
                    .build();
        }
        if (researchStudyService.getResearchStudyArchiveStatus(studyId)) {
            LOG.warn("Access denied: Study {} is archived.", studyId);

            Response response = Response.builder()
                    .status("failure")
                    .message("The study is archived; edits and updates are not allowed.")
                    .build();

            return response;
        }
        try {
            JSONB result = researchStudyService.updateVisibility(studyId, studyVisibilityId);
            return Response.builder()
                    .data(Map.of("response", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Successfully updated research study visibility")
                    .errors(null)
                    .build();
        } catch (IllegalArgumentException e) {
            LOG.error("Validation error: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to update research study visibility due to invalid input.")
                    .errors("Failed to update research study visibility due to invalid input: " + e
                            .getMessage())
                    .build();
        } catch (Exception e) {
            LOG.error("Error while calling the function: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to update research study visibility.")
                    .errors("Failed to update research study visibility: " + e
                            .getMessage())
                    .build();
        }
    }

    @PutMapping("/research-studies/delete-study/{studyId}")
    @Operation(summary = "Mark a research study as deleted by updating its status")
    @ResponseBody
    public Response deleteResearchStudy(@PathVariable String studyId) {
        try {
            if (!userNameService.getCurrentuserPartyId()
                    .equalsIgnoreCase(researchStudyService.getStudyOwner(studyId))) {

                LOG.warn("Access denied: User {} is not the owner of study {}", userNameService.getCurrentuserPartyId(),
                        studyId);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("Access denied: Only the study owner is permitted to delete this research study.")

                        .errors(null)
                        .build();
            }
            JSONB response = researchStudyService.deleteResearchStudy(studyId);
            return Response.builder()
                    .data(Map.of("response", JsonUtils.jsonStringToMapOrList(response.data())))
                    .status("success")
                    .message("Successfully set a research study as deleted.")
                    .errors(null)
                    .build();
        } catch (IllegalArgumentException e) {
            LOG.error("Validation error: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to delete research study due to invalid input.")
                    .errors("Failed to delete research study due to invalid input: "
                            + e.getMessage())
                    .build();
        } catch (Exception e) {
            LOG.error("Error while calling the function: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to delete research study.")
                    .errors("Failed to delete research study: "
                            + e.getMessage())
                    .build();

        }
    }

    @GetMapping("/research-study/archive-status")
    @ResponseBody
    @Operation(summary = "find archive status of the Research Study")
    public Response getResearchStudyArchiveStatus(@RequestParam String studyId) {
        LOG.info("get all research study team: {}", studyId);
        try {
            Object result = researchStudyService.getResearchStudyArchiveStatus(studyId);
            return Response.builder()
                    .data(Map.of("archiveStatus", result))
                    .status("success")
                    .message("Successfully read archive status of the Research Study")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to read archive status of the Research Study")
                    .errors("Error in reading archive status of the Research Study: " +
                            e.getMessage())
                    .build();
        }
    }

    @GetMapping("/research-study/{studyDisplayId}/exists")
    @ResponseBody
    @Operation(summary = "Check if a study display ID already exists")
    public Response checkIfStudyDisplayIdExists(@PathVariable String studyDisplayId) {
        LOG.info("Checking existence of study display ID: {}", studyDisplayId);
        try {
            boolean exists = researchStudyService.checkStudyDisplayIdExists(studyDisplayId);
            return Response.builder()
                    .data(Map.of("exists", exists))
                    .status("success")
                    .message(exists ? "Study display ID already exists" : "Study display ID is available for use")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to check study display ID")
                    .errors("Error while checking study display ID: " + e.getMessage())
                    .build();
        }
    }

    @GetMapping("/research-study/co-investigator")
    @ResponseBody
    @Operation(summary = "find Co investigator of Study Team")
    public Response getAllResearchStudyCoInvestigator(@RequestParam String studyId) {
        LOG.info("get all Co Investigators of Study Team: {}", studyId);
        try {
            JSONB result = researchStudyService.getAllResearchStudyCoInvestigator(studyId);
            return Response.builder()
                    .data(Map.of("studyTeam", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Successfully Read Co Investigators of Study Team")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to read all Co Investigators of Study Team")
                    .errors("Error in reading Co Investigators of Study Team: " +
                            e.getMessage())
                    .build();
        }
    }

    @GetMapping("/research-study/co-author")
    @ResponseBody
    @Operation(summary = "find Co author of Study Team")
    public Response getAllResearchStudyCoAuthor(@RequestParam String studyId) {
        LOG.info("get all Co Authors of Study Team: {}", studyId);
        try {
            JSONB result = researchStudyService.getAllResearchStudyCoAuthor(studyId);
            return Response.builder()
                    .data(Map.of("studyTeam", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Successfully Read Co Authors of Study Team")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to read all Co Authors of Study Team")
                    .errors("Error in reading Co Authors of Study Team: " +
                            e.getMessage())
                    .build();
        }
    }

    @GetMapping("/research-study/principal-author")
    @ResponseBody
    @Operation(summary = "find Principal author of Study Team")
    public Response getAllResearchStudyPrincipalAuthor(@RequestParam String studyId) {
        LOG.info("get all Principal Authors of Study Team: {}", studyId);
        try {
            JSONB result = researchStudyService.getAllResearchStudyPrincipalAuthor(studyId);
            return Response.builder()
                    .data(Map.of("studyTeam", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Successfully Read Principal Author of Study Team")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to read all Principal Authors of Study Team")
                    .errors("Error in reading Principal Authors of Study Team: " +
                            e.getMessage())
                    .build();
        }
    }

    @GetMapping("/research-study/study-team-members")
    @ResponseBody
    @Operation(summary = "Retrieve members of the study team")
    public Response getStudyTeamMembers(@RequestParam String studyId) {
        LOG.info("Fetching study team members for studyId: {}", studyId);
        try {
            JSONB result = researchStudyService.getStudyTeamMembers(studyId);
            return Response.builder()
                    .data(Map.of("studyTeam", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Successfully retrieved study team members.")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error retrieving study team members for studyId {}: {}", studyId, e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to retrieve study team members.")
                    .errors("Error: " + e.getMessage())
                    .build();
        }
    }

    @GetMapping("/research-study/nominated-principal-investigator")
    @ResponseBody
    @Operation(summary = "Retrieve the Nominated Principal Investigator (PI) for a study")
    public Response fetchNominatedPrincipalInvestigator(@RequestParam String studyId) {
        LOG.info("Fetching Nominated Principal Investigator for studyId: {}", studyId);
        try {
            JSONB result = researchStudyService.getNominatedPrincipalInvestigator(studyId);
            return Response.builder()
                    .data(Map.of("studyTeam", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Successfully retrieved Nominated Principal Investigator.")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error retrieving Nominated Principal Investigator for studyId {}: {}", studyId, e.getMessage(),
                    e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to retrieve Nominated Principal Investigator.")
                    .errors("Error: " + e.getMessage())
                    .build();
        }
    }

    @GetMapping("/research-study/study-owner")
    @ResponseBody
    @Operation(summary = "Retrieve the owner of a Research Study")
    public Response getStudyOwner(@RequestParam String studyId) {
        LOG.info("Retrieving owner of research study: {}", studyId);
        try {
            Object result = researchStudyService.getStudyOwner(studyId);
            return Response.builder()
                    .data(Map.of("studyOwner", result))
                    .status("success")
                    .message("Successfully retrieved study owner")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to retrieve study owner")
                    .errors("Error retrieving study owner: " + e.getMessage())
                    .build();
        }
    }

    @GetMapping("/research-study/citations")
    @ResponseBody
    @Operation(summary = "Get citations for a given study")
    public Response getResearchStudyCitations(@RequestParam String studyId) {
        LOG.info("Fetching citations for studyId: {}", studyId);
        try {
            JSONB citations = researchStudyService.getStudyCitations(studyId).join();

            return Response.builder()
                    .status("success")
                    .message("Citations fetched successfully")
                    .data(Map.of("citations", JsonUtils.jsonStringToMapOrList(citations.data())))
                    // .data(Map.of("citations", citations.data()))
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Failed to fetch citations", e);
            return Response.builder()
                    .status("error")
                    .message("Failed to fetch citations")
                    .data(Collections.emptyMap())
                    .errors(e.getMessage())
                    .build();
        }
    }

    @PutMapping("/research-study/citations/{citationId}")
    @ResponseBody
    @Operation(summary = "Update citation for a given study")
    public Response updateStudyCitation(@PathVariable String citationId,
            @RequestBody PublicationUpdateRequest request) {
        String studyId = request.collaboration_team().studyId();
        Map<String, Object> responseMap = new HashMap<>();

        LOG.info("Saving citations for studyId: {}", studyId);
        try {
            if (!userNameService.getCurrentuserPartyId()
                    .equalsIgnoreCase(researchStudyService.getStudyOwner(studyId))) {
                LOG.warn("Access denied: User {} is not the owner of study {}", userNameService.getCurrentuserPartyId(),
                        studyId);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("Access denied: Only the study owner is permitted to edit this research study.")
                        .errors(null)
                        .build();
            }
            Map<String, Object> citationResponse = researchStudyService.updateStudyCitation(citationId, request,
                    studyId);
            responseMap.put("citationResponse", citationResponse);

            if (!"success".equals(citationResponse.get("status"))) {
                return Response.builder()
                        .status("error")
                        .message("Failed to save citation")
                        .data(responseMap)
                        .errors(citationResponse.get("message").toString())
                        .build();
            }

            String citationRecordId = (String) citationResponse.get("citation_id");
            if (citationRecordId == null) {
                throw new IllegalStateException("Citation ID not returned from saveStudyCitation");
            }
            LOG.info("Saving authors for studyId: {}, citationId: {}", studyId, citationId);
            Object authorResponse = researchStudyService.saveAuthors(
                    studyId,
                    request.collaboration_team().coAuthorsName(),
                    citationId);

            responseMap.put("authorResponse", authorResponse.toString());

            return Response.builder()
                    .status("success")
                    .message("Citations and authors saved successfully")
                    .data(responseMap)
                    .errors(null)
                    .build();

        } catch (Exception e) {
            LOG.error("Failed to save citations", e);
            return Response.builder()
                    .status("error")
                    .message("Failed to save citations")
                    .data(Collections.emptyMap())
                    .errors(e.getMessage())
                    .build();
        }
    }

    @PostMapping("/research-study/citations")
    @ResponseBody
    @Operation(summary = "Save citation for a given study")
    public Response saveStudyCitation(@RequestBody PublicationUpdateRequest request) {
        String studyId = request.collaboration_team().studyId();
        Map<String, Object> responseMap = new HashMap<>();

        LOG.info("Saving citations for studyId: {}", studyId);
        try {
            if (!userNameService.getCurrentuserPartyId()
                    .equalsIgnoreCase(researchStudyService.getStudyOwner(studyId))) {
                LOG.warn("Access denied: User {} is not the owner of study {}", userNameService.getCurrentuserPartyId(),
                        studyId);
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("Access denied: Only the study owner is permitted to edit this research study.")
                        .errors(null)
                        .build();
            }
            final Boolean isDuplicate = researchStudyService.isDuplicatePubMedOrDOI(studyId, request.publication_doi(),
                    request.pubmed_id(), null);
            if (isDuplicate) {
                return Response.builder()
                        .status("error")
                        .message("Duplicate citation found")
                        .data(responseMap)
                        .errors("Duplicate citation found")
                        .build();

            }
            Map<String, Object> citationResponse = researchStudyService.saveStudyCitation(request, studyId);
            responseMap.put("citationResponse", citationResponse);

            if (!"success".equals(citationResponse.get("status"))) {
                return Response.builder()
                        .status("error")
                        .message("Failed to save citation")
                        .data(responseMap)
                        .errors(citationResponse.get("message").toString())
                        .build();
            }

            String citationId = (String) citationResponse.get("citation_id");
            if (citationId == null) {
                throw new IllegalStateException("Citation ID not returned from saveStudyCitation");
            }
            LOG.info("Saving authors for studyId: {}, citationId: {}", studyId, citationId);
            Object authorResponse = researchStudyService.saveAuthors(
                    studyId,
                    request.collaboration_team().coAuthorsName(),
                    citationId);

            responseMap.put("authorResponse", authorResponse.toString());

            return Response.builder()
                    .status("success")
                    .message("Citations and authors saved successfully")
                    .data(responseMap)
                    .errors(null)
                    .build();

        } catch (Exception e) {
            LOG.error("Failed to save citations", e);
            return Response.builder()
                    .status("error")
                    .message("Failed to save citations")
                    .data(Collections.emptyMap())
                    .errors(e.getMessage())
                    .build();
        }
    }

    @GetMapping("/research-study/citations/exist")
    @ResponseBody
    @Operation(summary = "Check given citation exists for the given study")
    public Response researchStudyCitationExists(@RequestParam String studyId,
            @RequestParam(required = false) String publicationDoi,
            @RequestParam(required = false) String pubmedId,
            @RequestParam(required = false) String citationId) {
        LOG.info("Fetching citations for studyId: {}", studyId);
        try {
            Map<String, Object> responseMap = new HashMap<>();
            final Boolean existCitation = researchStudyService.isDuplicatePubMedOrDOI(studyId, publicationDoi,
                    pubmedId, citationId);
            responseMap.put("existCitation", existCitation);
            return Response.builder()
                    .status("success")
                    .message("Citations fetched successfully")
                    .data(responseMap)
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Failed to fetch citation's exists status", e);
            return Response.builder()
                    .status("error")
                    .message("Failed to fetch citation existence status")
                    .data(Collections.emptyMap())
                    .errors(e.getMessage())
                    .build();
        }
    }
}
