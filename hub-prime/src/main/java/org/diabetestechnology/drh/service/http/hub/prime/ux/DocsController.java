package org.diabetestechnology.drh.service.http.hub.prime.ux;

import org.diabetestechnology.drh.service.http.hub.prime.route.RouteMapping;
import org.diabetestechnology.drh.service.http.pg.security.RequiresUserPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

@Controller
@Tag(name = "DRH Hub Docs API")
public class DocsController {
    private static final Logger LOG = LoggerFactory.getLogger(DocsController.class.getName());
    public static final ObjectMapper headersOM = JsonMapper.builder()
            .findAndAddModules()
            .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
            .build();

    private final Presentation presentation;

    public DocsController(final Presentation presentation) throws Exception {
        this.presentation = presentation;

    }

    @RouteMapping(label = "Documentation", siblingOrder = 100)
    @GetMapping("/docs")
    @RequiresUserPermission(permission = "DOCUMENTATION")
    public String docs() {
        return "redirect:/docs/swagger";
    }

    @RouteMapping(label = "DRH OpenAPI UI", title = "DRH OpenAPI Documentation", siblingOrder = 40)
    @GetMapping("/docs/swagger")
    @RequiresUserPermission(permission = "DRH_OPENAPI_UI")
    public String drhSwaggerUI(final Model model, final HttpServletRequest request) {
        if (!presentation.isAuthenticatedUser()) {
            LOG.info("Unauthorized access attempt to /docs/swagger-ui/drh-api");
            return presentation.populateModel("page/access-denied", model, request);
        } else if (!presentation.isMenuAdminUser()) {
            LOG.info("Authorized user has no permission to access /docs/swagger-ui/drh-api");
            return presentation.populateModel("page/access-denied", model, request);
        }
        String[] pageDescription = {
                "Offers detailed API documentation, outlining available endpoints and the interactions users can make with the data."
        };
        model.addAttribute("pageDescription", pageDescription);

        return presentation.populateModel("page/docs/swagger-ui/drh-api", model, request);
    }

    @RouteMapping(label = "Announcements", title = "Announcements", siblingOrder = 50)
    @GetMapping("/docs/announcements")
    @RequiresUserPermission(permission = "ANNOUNCEMENTS")
    public String announcements(final Model model, final HttpServletRequest request) {
        if (!presentation.isAuthenticatedUser()) {
            LOG.info("Unauthorized access attempt to /docs/announcements");
            return presentation.populateModel("page/access-denied", model, request);
        } else if (!presentation.isMenuAdminUser()) {
            LOG.info("Authorized user has no permission to access /docs/announcements");
            return presentation.populateModel("page/access-denied", model, request);
        }
        String[] pageDescription = {
                "Lists important updates, release notes, and new feature announcements to keep users informed about platform changes and enhancements."
        };
        model.addAttribute("pageDescription", pageDescription);

        return presentation.populateModel("page/docs/announcements", model, request);
    }

}
