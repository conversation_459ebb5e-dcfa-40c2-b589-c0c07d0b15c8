package org.diabetestechnology.drh.service.http.util;

import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.ActivityLog;
import org.diabetestechnology.drh.service.http.hub.prime.service.interaction.ActivityLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

@Component
public class LogoutFlagHandler implements LogoutHandler {
    private static final Logger LOG = LoggerFactory.getLogger(LogoutFlagHandler.class);
    private final ActivityLogService activityLogService;

    public LogoutFlagHandler(ActivityLogService activityLogService) {
        this.activityLogService = activityLogService;
    }

    @Override
    public void logout(HttpServletRequest request, HttpServletResponse response,
            Authentication authentication) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.setAttribute("LOGOUT_FLAG", true);
            prepareAndSaveActivityLog();
        }
    }

    private void prepareAndSaveActivityLog() {
        try {
            LOG.info("Preparing and saving logoutactivity log");
            final var requestUrl = "/logout";
            ActivityLog activityLog = activityLogService.getAuditDataFromRequestAndResponse(requestUrl);
            LOG.info("activityLog: {}", activityLog);
            activityLogService.saveActivityLog(activityLog);
        } catch (Exception e) {
            LOG.error("Error in prepareAndSaveActivityLog: {}", e.getMessage());
        }
    }
}
