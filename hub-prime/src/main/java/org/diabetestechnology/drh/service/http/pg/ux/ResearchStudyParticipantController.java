package org.diabetestechnology.drh.service.http.pg.ux;

import java.util.Map;

import org.diabetestechnology.drh.service.http.pg.request.ParticipantDataRequest;
import org.diabetestechnology.drh.service.http.pg.service.ParticipantService;
import org.diabetestechnology.drh.service.http.pg.service.ResearchStudyService;
import org.diabetestechnology.drh.service.http.util.JsonUtils;
import org.jooq.JSONB;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.diabetestechnology.drh.service.http.hub.prime.service.UserNameService;
import org.diabetestechnology.drh.service.http.pg.Response;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

import org.springframework.web.bind.annotation.PutMapping;

@Controller
@Tag(name = "DRH Participant APIs")
public class ResearchStudyParticipantController {
    private static final Logger LOG = LoggerFactory.getLogger(ResearchStudyParticipantController.class);

    private final ParticipantService participantService;
    private final ResearchStudyService researchStudyService;
    private final UserNameService userNameService;

    public ResearchStudyParticipantController(ParticipantService participantService,
            ResearchStudyService researchStudyService, UserNameService userNameService) {
        this.participantService = participantService;
        this.researchStudyService = researchStudyService;
        this.userNameService = userNameService;
    }

    @PostMapping("/participant")
    @ResponseBody
    @Operation(summary = "Save Participant")
    public Response saveParticipantData(
            @Valid @RequestBody ParticipantDataRequest request) {
        try {
            if (!userNameService.getCurrentuserPartyId()
                    .equalsIgnoreCase(researchStudyService.getStudyOwner(request.studyId()))) {
                LOG.warn("Access denied: User {} is not the owner of study {}", userNameService.getCurrentuserPartyId(),
                        request.studyId());
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("Access denied: Only the study owner is permitted to edit this research study.")
                        .errors(null)
                        .build();
            }
            if (researchStudyService.getResearchStudyArchiveStatus(request.studyId())) {
                LOG.warn("Access denied: Study {} is archived.", request.studyId());

                Response response = Response.builder()
                        .status("failure")
                        .message("The study is archived; edits and updates are not allowed.")
                        .build();

                return response;
            }
            LOG.info("Saving participant data: {}", request);
            JSONB result = participantService.saveParticipantData(request);
            LOG.info("Successfully saved participant: {}", result);
            return Response.builder()
                    .data(Map.of("practitionerProfile", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Participant data saved successfully")
                    .errors(null)
                    .build();
        } catch (IllegalArgumentException e) {
            LOG.warn("Validation or processing error: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to save participant data")
                    .errors(e.getMessage())
                    .build();
        } catch (Exception e) {
            LOG.error("Unexpected error while saving participant data: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("An unexpected error occurred")
                    .errors(e.getMessage())
                    .build();
        }
    }

    @PutMapping("/research-study-participant/{participantId}")
    @ResponseBody
    @Operation(summary = "Update Participant Data Inline")
    public Response updateParticipantDataInline(
            @PathVariable String participantId,
            @RequestBody JsonNode jsonInput) {
        try {
            JSONB result = participantService.updateParticipantDataInline(participantId,
                    JSONB.valueOf(jsonInput.toString()));
            return Response.builder()
                    .data(Map.of("participantData", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message("Participant data updated successfully")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error while updating participant data: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Error updating participant data")
                    .errors("Error in updating participant data: " + e.getMessage())
                    .build();
        }
    }

    @Operation(summary = "Retrieve participant details by participant ID")
    @ResponseBody
    @GetMapping("/research-study-participant/{participantId}")
    public Response getParticipantDetails(@PathVariable String participantId) {
        try {
            Map<String, Object> participantData = participantService.getParticipantDetails(participantId);

            return Response.builder()
                    .data(Map.of("participantData", participantData))
                    .status("success")
                    .message("Participant details retrieved successfully")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error while retrieving participant details for ID {}: {}", participantId, e.getMessage(), e);

            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Error retrieving participant details")
                    .errors("Error fetching participant details: " + e.getMessage())
                    .build();
        }
    }

    @GetMapping("/participant/{studyId}/{participantDisplayId}/exists")
    @ResponseBody
    @Operation(summary = "Check if a participant display ID already exists")
    public Response checkIfParticipantDisplayIdExists(@PathVariable String studyId,
            @PathVariable String participantDisplayId) {
        LOG.info("Checking existence of participant display ID: {}", participantDisplayId);
        try {
            boolean exists = participantService.checkIfParticipantDisplayIdExists(studyId, participantDisplayId);
            return Response.builder()
                    .data(Map.of("exists", exists))
                    .status("success")
                    .message(exists ? "participant display ID already exists"
                            : "participant display ID is available for use")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Failed to check participant display ID")
                    .errors("Error while checking participant display ID: " + e.getMessage())
                    .build();
        }
    }

    @SuppressWarnings("unchecked")
    @PutMapping("/participant-settings/{participantId}")
    @ResponseBody
    @Operation(summary = "Update Participant Data")
    public Response updateParticipantData(@PathVariable String participantId,
            @Valid @RequestBody ParticipantDataRequest request) throws JsonProcessingException {
        try {
            if (!userNameService.getCurrentuserPartyId()
                    .equalsIgnoreCase(researchStudyService.getStudyOwner(request.studyId()))) {
                LOG.warn("Access denied: User {} is not the owner of study {}", userNameService.getCurrentuserPartyId(),
                        request.studyId());
                return Response.builder()
                        .data(Map.of())
                        .status("error")
                        .message("Access denied: Only the study owner is permitted to edit this research study.")
                        .errors(null)
                        .build();
            }
            if (researchStudyService.getResearchStudyArchiveStatus(request.studyId())) {
                LOG.warn("Access denied: Study {} is archived.", request.studyId());

                Response response = Response.builder()
                        .status("failure")
                        .message("The study is archived; edits and updates are not allowed.")
                        .build();

                return response;
            }
            LOG.info("Updating participant data: {}", request);
            JSONB result = participantService.updateParticipantData(participantId, request);
            LOG.info("Successfully updated participant data for participantId={}. Result: {}", participantId, result);
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> resultMap = objectMapper.readValue(result.data(), Map.class);
            return Response.builder()
                    .data(Map.of("participantData", JsonUtils.jsonStringToMapOrList(result.data())))
                    .status("success")
                    .message(resultMap.containsKey("message") ? resultMap.get("message").toString()
                            : "Unknown response")
                    .errors(null)
                    .build();
        } catch (Exception e) {
            LOG.error("Error while updating participant data: {}", e.getMessage(), e);
            return Response.builder()
                    .data(Map.of())
                    .status("error")
                    .message("Error updating participant data")
                    .errors("Error in updating participant data: " + e.getMessage())
                    .build();
        }
    }

}
